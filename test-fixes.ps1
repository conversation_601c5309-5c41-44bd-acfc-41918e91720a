# Test script to verify the fixes for WiFi Hacker Windows

Write-Host "Testing WiFi Hacker Windows Fixes" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green
Write-Host ""

# Test 1: Check if the parameter conflict is fixed
Write-Host "Test 1: Parameter Conflict Fix" -ForegroundColor Yellow
try {
    # Try to load the script without executing it
    $scriptContent = Get-Content "wifi-hacker-windows.ps1" -Raw
    
    # Check if the problematic line is fixed
    if ($scriptContent -match '\$script:ScriptVersion = "1.0"') {
        Write-Host "✓ Parameter conflict fixed - using ScriptVersion instead of Version" -ForegroundColor Green
    } else {
        Write-Host "✗ Parameter conflict not fixed" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Error checking script: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 2: Test WiFi interface detection
Write-Host "Test 2: WiFi Interface Detection" -ForegroundColor Yellow
try {
    $interfaces = Get-NetAdapter | Where-Object {
        $_.InterfaceDescription -like "*Wireless*" -or
        $_.InterfaceDescription -like "*WiFi*" -or
        $_.InterfaceDescription -like "*802.11*" -or
        $_.InterfaceDescription -like "*Wi-Fi*" -or
        $_.InterfaceDescription -like "*MT7902*" -or
        $_.InterfaceDescription -like "*MediaTek*" -or
        $_.InterfaceDescription -like "*Wireless LAN*" -or
        $_.PhysicalMediaType -eq "Native 802.11"
    }
    
    # Ensure we have an array
    if ($interfaces) {
        if ($interfaces -isnot [array]) {
            $interfaces = @($interfaces)
        }
    } else {
        $interfaces = @()
    }
    
    Write-Host "Found $($interfaces.Count) WiFi interface(s):" -ForegroundColor Cyan
    
    if ($interfaces.Count -gt 0) {
        $index = 1
        foreach ($interface in $interfaces) {
            $status = if ($interface.Status -eq "Up") { "Connected" } else { "Disconnected" }
            Write-Host "$index) $($interface.Name) - $($interface.InterfaceDescription) [$status]" -ForegroundColor White
            $index++
        }
        Write-Host "✓ WiFi interface detection working" -ForegroundColor Green
    } else {
        Write-Host "✗ No WiFi interfaces found" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Error detecting WiFi interfaces: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 3: Test array handling
Write-Host "Test 3: Array Handling" -ForegroundColor Yellow
try {
    # Simulate the interface selection logic
    $testInterfaces = Get-NetAdapter | Where-Object {
        $_.InterfaceDescription -like "*Wireless*" -or
        $_.InterfaceDescription -like "*WiFi*" -or
        $_.InterfaceDescription -like "*MT7902*"
    }
    
    if ($testInterfaces) {
        if ($testInterfaces -isnot [array]) {
            $testInterfaces = @($testInterfaces)
        }
        
        Write-Host "Array handling test:" -ForegroundColor Cyan
        Write-Host "  Count: $($testInterfaces.Count)" -ForegroundColor White
        Write-Host "  Type: $($testInterfaces.GetType().Name)" -ForegroundColor White
        
        # Test indexing
        if ($testInterfaces.Count -gt 0) {
            $firstInterface = $testInterfaces[0]
            Write-Host "  First interface: $($firstInterface.Name)" -ForegroundColor White
            Write-Host "✓ Array indexing working" -ForegroundColor Green
        }
    } else {
        Write-Host "⚠ No interfaces to test array handling" -ForegroundColor Yellow
    }
} catch {
    Write-Host "✗ Error testing array handling: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 4: Test basic WiFi commands
Write-Host "Test 4: Basic WiFi Commands" -ForegroundColor Yellow
try {
    # Test netsh wlan commands
    $wlanInterfaces = netsh wlan show interfaces 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ netsh wlan show interfaces - working" -ForegroundColor Green
    } else {
        Write-Host "✗ netsh wlan show interfaces - failed" -ForegroundColor Red
    }
    
    $wlanProfiles = netsh wlan show profiles 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ netsh wlan show profiles - working" -ForegroundColor Green
    } else {
        Write-Host "✗ netsh wlan show profiles - failed" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Error testing WiFi commands: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 5: Administrator privileges
Write-Host "Test 5: Administrator Privileges" -ForegroundColor Yellow
$currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
$principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
$isAdmin = $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if ($isAdmin) {
    Write-Host "✓ Running with Administrator privileges" -ForegroundColor Green
} else {
    Write-Host "⚠ Not running as Administrator (some features may not work)" -ForegroundColor Yellow
}

Write-Host ""

# Summary
Write-Host "Test Summary:" -ForegroundColor Cyan
Write-Host "=============" -ForegroundColor Cyan
Write-Host ""

if ($isAdmin) {
    Write-Host "✓ Administrator privileges: OK" -ForegroundColor Green
} else {
    Write-Host "⚠ Administrator privileges: Missing" -ForegroundColor Yellow
}

$wifiCount = (Get-NetAdapter | Where-Object {
    $_.InterfaceDescription -like "*Wireless*" -or
    $_.InterfaceDescription -like "*WiFi*" -or
    $_.InterfaceDescription -like "*MT7902*"
}).Count

if ($wifiCount -gt 0) {
    Write-Host "✓ WiFi interfaces: $wifiCount found" -ForegroundColor Green
} else {
    Write-Host "✗ WiFi interfaces: None found" -ForegroundColor Red
}

Write-Host ""
Write-Host "The main script should now work without the parameter conflict error." -ForegroundColor Green
Write-Host "Try running: .\wifi-hacker-windows.ps1" -ForegroundColor White
Write-Host ""
Write-Host "If you still have issues, try:" -ForegroundColor Yellow
Write-Host "1. Run as Administrator" -ForegroundColor White
Write-Host "2. Check PowerShell execution policy: Get-ExecutionPolicy" -ForegroundColor White
Write-Host "3. Set execution policy if needed: Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser" -ForegroundColor White
