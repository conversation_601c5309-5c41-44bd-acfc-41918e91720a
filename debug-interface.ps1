# Debug script for WiFi interface selection issue

Write-Host "Debugging WiFi Interface Selection" -ForegroundColor Yellow
Write-Host "==================================" -ForegroundColor Yellow
Write-Host ""

# Test 1: Get all network adapters
Write-Host "Test 1: All Network Adapters" -ForegroundColor Green
$allAdapters = Get-NetAdapter
Write-Host "Total adapters found: $($allAdapters.Count)" -ForegroundColor Cyan
foreach ($adapter in $allAdapters) {
    Write-Host "  - Name: $($adapter.Name)" -ForegroundColor White
    Write-Host "    Description: $($adapter.InterfaceDescription)" -ForegroundColor Gray
    Write-Host "    MediaType: $($adapter.PhysicalMediaType)" -ForegroundColor Gray
    Write-Host "    Status: $($adapter.Status)" -ForegroundColor Gray
    Write-Host ""
}

# Test 2: Filter for WiFi interfaces
Write-Host "Test 2: WiFi Interface Detection" -ForegroundColor Green
$wifiInterfaces = Get-NetAdapter | Where-Object {
    $_.InterfaceDescription -like "*Wireless*" -or
    $_.InterfaceDescription -like "*WiFi*" -or
    $_.InterfaceDescription -like "*802.11*" -or
    $_.InterfaceDescription -like "*Wi-Fi*" -or
    $_.InterfaceDescription -like "*MT7902*" -or
    $_.InterfaceDescription -like "*Wireless LAN*" -or
    $_.PhysicalMediaType -eq "Native 802.11"
}

Write-Host "WiFi interfaces found: $($wifiInterfaces.Count)" -ForegroundColor Cyan

if ($wifiInterfaces) {
    # Check if it's an array or single object
    Write-Host "Type: $($wifiInterfaces.GetType().Name)" -ForegroundColor Gray
    
    # Convert to array if needed
    if ($wifiInterfaces.GetType().Name -ne "Object[]") {
        Write-Host "Converting single object to array..." -ForegroundColor Yellow
        $wifiInterfaces = @($wifiInterfaces)
    }
    
    Write-Host "After conversion - Count: $($wifiInterfaces.Count)" -ForegroundColor Cyan
    Write-Host "After conversion - Type: $($wifiInterfaces.GetType().Name)" -ForegroundColor Gray
    
    # Display interfaces
    $index = 1
    foreach ($interface in $wifiInterfaces) {
        $status = if ($interface.Status -eq "Up") { "Connected" } else { "Disconnected" }
        Write-Host "$index) $($interface.Name) - $($interface.InterfaceDescription) [$status]" -ForegroundColor White
        $index++
    }
    
    # Test selection
    Write-Host ""
    Write-Host "Test 3: Interface Selection" -ForegroundColor Green
    $selection = Read-Host "Select interface number (1-$($wifiInterfaces.Count))"
    
    try {
        $selectedIndex = [int]$selection - 1
        Write-Host "Selection: $selection" -ForegroundColor Cyan
        Write-Host "Index: $selectedIndex" -ForegroundColor Cyan
        Write-Host "Count: $($wifiInterfaces.Count)" -ForegroundColor Cyan
        Write-Host "Valid range: 0 to $($wifiInterfaces.Count - 1)" -ForegroundColor Cyan
        
        if ($selectedIndex -ge 0 -and $selectedIndex -lt $wifiInterfaces.Count) {
            $selectedInterface = $wifiInterfaces[$selectedIndex]
            Write-Host "SUCCESS: Selected interface: $($selectedInterface.Name)" -ForegroundColor Green
            Write-Host "Description: $($selectedInterface.InterfaceDescription)" -ForegroundColor Green
        } else {
            Write-Host "ERROR: Index $selectedIndex is out of range!" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "ERROR: Invalid input - $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "No WiFi interfaces found!" -ForegroundColor Red
}

Write-Host ""
Write-Host "Debug completed!" -ForegroundColor Green
