# WiFi Hacker for Windows

A Windows PowerShell adaptation of the popular Linux WiFi penetration testing tool. This version provides both basic Windows-native WiFi tools and advanced features through WSL (Windows Subsystem for Linux).

## ⚠️ LEGAL DISCLAIMER

**THIS TOOL IS FOR EDUCATIONAL AND <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> TESTING PURPOSES ONLY!**

- Only use on networks you own or have explicit written permission to test
- Unauthorized access to computer networks is illegal and punishable by law
- Users are responsible for complying with all applicable laws and regulations
- The authors are not responsible for any misuse of this tool

## 🚀 Features

### Basic Features (Windows Native)
- View saved WiFi passwords
- Scan for available networks
- Connect/disconnect from networks
- Manage WiFi profiles
- Export network configurations
- Network information display

### Advanced Features (WSL Required)
- Monitor mode setup
- WPA/WPA2 handshake capture
- Dictionary attacks using aircrack-ng
- WPS attacks using reaver
- Full aircrack-ng suite integration

## 📋 Requirements

### Minimum Requirements
- Windows 10 or Windows 11
- PowerShell 5.0 or later
- Administrator privileges (for advanced features)

### For Advanced Features
- WSL (Windows Subsystem for Linux)
- Linux distribution (Kali Linux recommended)
- Compatible WiFi adapter with monitor mode support
- Aircrack-ng suite installed in WSL

## 🛠️ Installation

### Quick Start
1. Download the files:
   - `wifi-hacker-windows.ps1`
   - `wifi-hacker-windows.bat`

2. Right-click `wifi-hacker-windows.bat` and select "Run as Administrator"

### WSL Setup (for Advanced Features)
1. Install WSL:
   ```cmd
   wsl --install
   ```

2. Install Kali Linux (recommended):
   ```cmd
   wsl --install -d kali-linux
   ```

3. Update the system:
   ```bash
   sudo apt update && sudo apt upgrade -y
   ```

4. Install aircrack-ng suite:
   ```bash
   sudo apt install -y aircrack-ng reaver wifite hashcat
   ```

## 🎯 Usage

### Running the Tool
1. **Using Batch File (Recommended):**
   - Right-click `wifi-hacker-windows.bat`
   - Select "Run as Administrator"

2. **Using PowerShell Directly:**
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process
   .\wifi-hacker-windows.ps1
   ```

### Command Line Options
```powershell
.\wifi-hacker-windows.ps1 -Help     # Show help information
.\wifi-hacker-windows.ps1 -Version  # Show version information
```

## 📖 Menu Guide

### Main Menu
1. **Select WiFi Interface** - Choose your WiFi adapter
2. **Scan WiFi Networks** - Discover available networks
3. **Network Information** - View current connection details
4. **Basic WiFi Tools** - Windows-native WiFi utilities
5. **Advanced Tools** - WSL-based penetration testing tools
6. **Help & Documentation** - Usage instructions
7. **Exit** - Close the application

### Basic Tools Menu
1. **View WiFi Passwords** - Show saved network passwords
2. **Disconnect from Current Network** - Disconnect WiFi
3. **Connect to Network** - Connect to a specific network
4. **Forget Network Profile** - Remove saved profiles
5. **Export WiFi Profiles** - Backup network configurations

### Advanced Tools Menu (WSL Required)
1. **Install Aircrack-ng Suite** - Auto-install penetration testing tools
2. **Monitor Mode Setup** - Configure WiFi adapter for monitoring
3. **Capture Handshakes** - Capture WPA/WPA2 authentication data
4. **Dictionary Attack** - Crack passwords using wordlists
5. **WPS Attack** - Attack WPS-enabled networks

## 🔧 Troubleshooting

### Common Issues

**"PowerShell is not available"**
- Ensure PowerShell 5.0+ is installed
- Try running from PowerShell ISE

**"Not running as Administrator"**
- Right-click the batch file and select "Run as Administrator"
- Some features require elevated privileges

**"WSL is not available"**
- Install WSL: `wsl --install`
- Restart your computer after installation

**"Aircrack-ng suite not available"**
- Install in WSL: `wsl sudo apt install aircrack-ng reaver wifite`
- Ensure WSL distribution is running

**Monitor mode not working**
- Check if your WiFi adapter supports monitor mode
- Some adapters require specific drivers
- Consider using a dedicated penetration testing adapter

### Compatible WiFi Adapters
For monitor mode support, consider these adapters:
- Alfa AWUS036ACS (Dual-band AC600)
- Alfa AWUS036NHA (High-power N150)
- Panda PAU09 (Budget option)
- TP-Link AC600 T2U Plus

## 📁 File Structure

```
wifi-hacker-windows/
├── wifi-hacker-windows.ps1    # Main PowerShell script
├── wifi-hacker-windows.bat    # Batch launcher
├── README-Windows.md           # This documentation
└── Sessions/                   # Created automatically
    ├── WiFiProfiles/          # Exported profiles
    └── Captures/              # Handshake captures
```

## 🔒 Security Notes

- Always run with minimum required privileges
- Session files may contain sensitive information
- Clear session data when finished testing
- Use strong passwords for your own networks
- Keep the tool updated for security patches

## 🤝 Contributing

This is an adaptation of the original Linux WiFi Hacker by esc0rtd3w. 

Original project: https://github.com/esc0rtd3w/wifi-hacker

## 📄 License

This tool is provided for educational purposes. Users must comply with all applicable laws and regulations.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section
2. Verify your system meets the requirements
3. Ensure proper installation of dependencies

---

**Remember: Use this tool responsibly and only on networks you own or have permission to test!**
