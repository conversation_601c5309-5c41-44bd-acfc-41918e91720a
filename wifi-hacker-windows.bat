@echo off
REM WiFi Hacker for Windows - Batch Launcher
REM This script launches the PowerShell version of WiFi Hacker

echo.
echo ================================================================
echo    WiFi Hacker for Windows - Launcher
echo    Educational and Authorized Testing Only!
echo ================================================================
echo.

REM Check if PowerShell is available
powershell -Command "Get-Host" >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: PowerShell is not available or not working properly!
    echo Please ensure PowerShell 5.0 or later is installed.
    pause
    exit /b 1
)

REM Check if running as administrator
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: Not running as Administrator!
    echo Some features may not work properly.
    echo Right-click and "Run as Administrator" for full functionality.
    echo.
    pause
)

REM Check if the PowerShell script exists
if not exist "wifi-hacker-windows.ps1" (
    echo ERROR: wifi-hacker-windows.ps1 not found!
    echo Please ensure the PowerShell script is in the same directory.
    pause
    exit /b 1
)

echo Starting WiFi Hacker for Windows...
echo.

REM Set execution policy temporarily and run the script
powershell -ExecutionPolicy Bypass -File "wifi-hacker-windows.ps1"

echo.
echo WiFi Hacker session ended.
pause
