# WiFi Hacker for Windows - Test Script
# This script tests the functionality of the Windows adaptation

Write-Host "Testing WiFi Hacker for Windows..." -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green
Write-Host ""

# Test 1: PowerShell Version
Write-Host "Test 1: PowerShell Version" -ForegroundColor Yellow
Write-Host "PowerShell Version: $($PSVersionTable.PSVersion)" -ForegroundColor White
if ($PSVersionTable.PSVersion.Major -ge 5) {
    Write-Host "✓ PowerShell version is compatible" -ForegroundColor Green
} else {
    Write-Host "✗ PowerShell version too old (5.0+ required)" -ForegroundColor Red
}
Write-Host ""

# Test 2: Administrator Rights
Write-Host "Test 2: Administrator Rights" -ForegroundColor Yellow
$currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
$principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
$isAdmin = $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if ($isAdmin) {
    Write-Host "✓ Running with Administrator privileges" -ForegroundColor Green
} else {
    Write-Host "⚠ Not running as Administrator (some features may not work)" -ForegroundColor Yellow
}
Write-Host ""

# Test 3: Required Files
Write-Host "Test 3: Required Files" -ForegroundColor Yellow
$requiredFiles = @(
    "wifi-hacker-windows.ps1",
    "wifi-hacker-windows.bat",
    "README-Windows.md"
)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        Write-Host "✓ $file found" -ForegroundColor Green
    } else {
        Write-Host "✗ $file missing" -ForegroundColor Red
    }
}
Write-Host ""

# Test 4: WiFi Interfaces
Write-Host "Test 4: WiFi Interfaces" -ForegroundColor Yellow
try {
    $wifiInterfaces = Get-NetAdapter | Where-Object { 
        $_.InterfaceDescription -like "*Wireless*" -or 
        $_.InterfaceDescription -like "*WiFi*" -or 
        $_.InterfaceDescription -like "*802.11*" 
    }
    
    if ($wifiInterfaces.Count -gt 0) {
        Write-Host "✓ Found $($wifiInterfaces.Count) WiFi interface(s):" -ForegroundColor Green
        foreach ($interface in $wifiInterfaces) {
            $status = if ($interface.Status -eq "Up") { "Connected" } else { "Disconnected" }
            Write-Host "  - $($interface.Name): $($interface.InterfaceDescription) [$status]" -ForegroundColor White
        }
    } else {
        Write-Host "✗ No WiFi interfaces found" -ForegroundColor Red
    }
}
catch {
    Write-Host "✗ Error checking WiFi interfaces: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Test 5: WSL Availability
Write-Host "Test 5: WSL Availability" -ForegroundColor Yellow
try {
    $wslCheck = wsl --list --quiet 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ WSL is available" -ForegroundColor Green
        
        # Check for Kali Linux
        $distributions = wsl --list --quiet
        if ($distributions -match "kali-linux") {
            Write-Host "✓ Kali Linux distribution found" -ForegroundColor Green
        } else {
            Write-Host "⚠ Kali Linux not installed (advanced features unavailable)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "✗ WSL not available" -ForegroundColor Red
    }
}
catch {
    Write-Host "✗ WSL not available" -ForegroundColor Red
}
Write-Host ""

# Test 6: Aircrack-ng Suite (if WSL available)
Write-Host "Test 6: Aircrack-ng Suite" -ForegroundColor Yellow
try {
    $aircrackCheck = wsl which aircrack-ng 2>$null
    if ($LASTEXITCODE -eq 0 -and $aircrackCheck) {
        Write-Host "✓ Aircrack-ng suite is available" -ForegroundColor Green
        
        # Check individual tools
        $tools = @("aircrack-ng", "airodump-ng", "aireplay-ng", "reaver", "wifite")
        foreach ($tool in $tools) {
            $toolCheck = wsl which $tool 2>$null
            if ($LASTEXITCODE -eq 0 -and $toolCheck) {
                Write-Host "  ✓ $tool available" -ForegroundColor Green
            } else {
                Write-Host "  ✗ $tool not found" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "✗ Aircrack-ng suite not available" -ForegroundColor Red
        Write-Host "  Run install-windows.ps1 to install required tools" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "✗ Cannot check aircrack-ng suite (WSL may not be available)" -ForegroundColor Red
}
Write-Host ""

# Test 7: Network Commands
Write-Host "Test 7: Network Commands" -ForegroundColor Yellow
try {
    # Test netsh command
    $netshTest = netsh wlan show interfaces 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ netsh wlan commands available" -ForegroundColor Green
    } else {
        Write-Host "✗ netsh wlan commands failed" -ForegroundColor Red
    }
    
    # Test basic connectivity
    $pingTest = Test-Connection -ComputerName "*******" -Count 1 -Quiet
    if ($pingTest) {
        Write-Host "✓ Internet connectivity available" -ForegroundColor Green
    } else {
        Write-Host "⚠ No internet connectivity" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "✗ Error testing network commands: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Test 8: Session Directory
Write-Host "Test 8: Session Directory" -ForegroundColor Yellow
$sessionPath = "$env:USERPROFILE\Documents\WiFiHacker\Sessions"
try {
    if (!(Test-Path $sessionPath)) {
        New-Item -ItemType Directory -Path $sessionPath -Force | Out-Null
    }
    
    if (Test-Path $sessionPath) {
        Write-Host "✓ Session directory available: $sessionPath" -ForegroundColor Green
    } else {
        Write-Host "✗ Cannot create session directory" -ForegroundColor Red
    }
}
catch {
    Write-Host "✗ Error with session directory: $($_.Exception.Message)" -ForegroundColor Red
}
Write-Host ""

# Summary
Write-Host "Test Summary" -ForegroundColor Cyan
Write-Host "============" -ForegroundColor Cyan

$basicFeaturesReady = $PSVersionTable.PSVersion.Major -ge 5 -and (Get-NetAdapter | Where-Object { $_.InterfaceDescription -like "*Wireless*" }).Count -gt 0
$advancedFeaturesReady = $basicFeaturesReady -and (try { wsl which aircrack-ng 2>$null; $LASTEXITCODE -eq 0 } catch { $false })

if ($basicFeaturesReady) {
    Write-Host "✓ Basic features ready" -ForegroundColor Green
} else {
    Write-Host "✗ Basic features not ready" -ForegroundColor Red
}

if ($advancedFeaturesReady) {
    Write-Host "✓ Advanced features ready" -ForegroundColor Green
} else {
    Write-Host "⚠ Advanced features not ready (install WSL and aircrack-ng)" -ForegroundColor Yellow
}

Write-Host ""
if ($basicFeaturesReady) {
    Write-Host "You can run the WiFi Hacker with: .\wifi-hacker-windows.bat" -ForegroundColor Green
} else {
    Write-Host "Please install missing components before running WiFi Hacker" -ForegroundColor Yellow
}

if (!$advancedFeaturesReady) {
    Write-Host "For advanced features, run: .\install-windows.ps1 -All" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Test completed!" -ForegroundColor Green
