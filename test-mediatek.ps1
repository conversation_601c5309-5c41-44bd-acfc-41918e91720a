# Test script specifically for MediaTek WiFi adapters

Write-Host "Testing MediaTek WiFi Adapter Detection" -ForegroundColor Green
Write-Host "=======================================" -ForegroundColor Green
Write-Host ""

# Function to get WiFi interfaces (same as in main script)
function Get-WiFiInterfacesTest {
    Write-Host "Searching for WiFi interfaces..." -ForegroundColor Yellow
    
    try {
        # Comprehensive search for WiFi interfaces
        $interfaces = Get-NetAdapter | Where-Object {
            $_.InterfaceDescription -like "*Wireless*" -or
            $_.InterfaceDescription -like "*WiFi*" -or
            $_.InterfaceDescription -like "*802.11*" -or
            $_.InterfaceDescription -like "*Wi-Fi*" -or
            $_.InterfaceDescription -like "*MT7902*" -or
            $_.InterfaceDescription -like "*MediaTek*" -or
            $_.InterfaceDescription -like "*Wireless LAN*" -or
            $_.PhysicalMediaType -eq "Native 802.11"
        }
        
        # Ensure we have an array
        if ($interfaces) {
            if ($interfaces -isnot [array]) {
                $interfaces = @($interfaces)
            }
        } else {
            $interfaces = @()
        }
        
        Write-Host "Found $($interfaces.Count) WiFi interface(s)" -ForegroundColor Cyan
        
        if ($interfaces.Count -eq 0) {
            Write-Host "No WiFi interfaces found!" -ForegroundColor Red
            Write-Host ""
            Write-Host "All network adapters:" -ForegroundColor Yellow
            Get-NetAdapter | ForEach-Object {
                Write-Host "  - Name: $($_.Name)" -ForegroundColor White
                Write-Host "    Description: $($_.InterfaceDescription)" -ForegroundColor Gray
                Write-Host "    MediaType: $($_.PhysicalMediaType)" -ForegroundColor Gray
                Write-Host "    Status: $($_.Status)" -ForegroundColor Gray
                Write-Host ""
            }
            return $null
        }
        
        # Display found interfaces
        $index = 1
        foreach ($interface in $interfaces) {
            $status = if ($interface.Status -eq "Up") { "Connected" } else { "Disconnected" }
            Write-Host "$index) $($interface.Name) - $($interface.InterfaceDescription) [$status]" -ForegroundColor White
            $index++
        }
        
        return $interfaces
    }
    catch {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Function to test interface selection
function Test-InterfaceSelection {
    param($interfaces)
    
    if (!$interfaces -or $interfaces.Count -eq 0) {
        Write-Host "No interfaces to test!" -ForegroundColor Red
        return
    }
    
    Write-Host ""
    Write-Host "Testing interface selection..." -ForegroundColor Yellow
    $selection = Read-Host "Select interface number (1-$($interfaces.Count))"
    
    if ([string]::IsNullOrWhiteSpace($selection)) {
        Write-Host "ERROR: No selection made!" -ForegroundColor Red
        return
    }
    
    try {
        $selectedIndex = [int]$selection - 1
        
        Write-Host ""
        Write-Host "Selection Analysis:" -ForegroundColor Cyan
        Write-Host "  Input: '$selection'" -ForegroundColor White
        Write-Host "  Parsed Index: $selectedIndex" -ForegroundColor White
        Write-Host "  Interface Count: $($interfaces.Count)" -ForegroundColor White
        Write-Host "  Valid Range: 0 to $($interfaces.Count - 1)" -ForegroundColor White
        Write-Host "  Array Type: $($interfaces.GetType().Name)" -ForegroundColor White
        
        # Ensure interfaces is treated as array
        if ($interfaces -isnot [array]) {
            Write-Host "  Converting to array..." -ForegroundColor Yellow
            $interfaces = @($interfaces)
        }
        
        if ($selectedIndex -ge 0 -and $selectedIndex -lt $interfaces.Count) {
            $selectedInterface = $interfaces[$selectedIndex]
            Write-Host ""
            Write-Host "SUCCESS! Selected interface:" -ForegroundColor Green
            Write-Host "  Name: $($selectedInterface.Name)" -ForegroundColor Green
            Write-Host "  Description: $($selectedInterface.InterfaceDescription)" -ForegroundColor Green
            Write-Host "  Status: $($selectedInterface.Status)" -ForegroundColor Green
            Write-Host "  MAC Address: $($selectedInterface.MacAddress)" -ForegroundColor Green
            
            # Test basic WiFi commands
            Write-Host ""
            Write-Host "Testing basic WiFi commands..." -ForegroundColor Yellow
            try {
                $wlanInfo = netsh wlan show interfaces
                Write-Host "netsh wlan show interfaces - SUCCESS" -ForegroundColor Green
            }
            catch {
                Write-Host "netsh wlan show interfaces - FAILED: $($_.Exception.Message)" -ForegroundColor Red
            }
            
        } else {
            Write-Host ""
            Write-Host "ERROR: Index $selectedIndex is out of valid range (0-$($interfaces.Count - 1))!" -ForegroundColor Red
        }
    }
    catch {
        Write-Host ""
        Write-Host "ERROR: Failed to parse selection '$selection'" -ForegroundColor Red
        Write-Host "Details: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Run the test
$wifiInterfaces = Get-WiFiInterfacesTest

if ($wifiInterfaces) {
    Test-InterfaceSelection $wifiInterfaces
} else {
    Write-Host "Cannot proceed with selection test - no WiFi interfaces found." -ForegroundColor Red
}

Write-Host ""
Write-Host "Test completed!" -ForegroundColor Green
Write-Host ""
Write-Host "If this test works but the main script doesn't, there may be an issue with the main script logic." -ForegroundColor Yellow
