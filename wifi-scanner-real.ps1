# Real WiFi Network Scanner for Windows
# This script can actually detect available WiFi networks, not just saved profiles

Write-Host "Real WiFi Network Scanner" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
Write-Host ""

function Get-AvailableWiFiNetworks {
    Write-Host "Scanning for available WiFi networks..." -ForegroundColor Yellow
    Write-Host ""
    
    try {
        # Method 1: Use WMI to get available networks
        Write-Host "Method 1: Using WMI (Windows Management Instrumentation)" -ForegroundColor Cyan
        
        try {
            # Get WiFi adapter
            $wifiAdapter = Get-WmiObject -Class Win32_NetworkAdapter | Where-Object { 
                $_.Name -like "*Wireless*" -or 
                $_.Name -like "*WiFi*" -or 
                $_.Name -like "*802.11*" -or
                $_.Name -like "*Wi-Fi*"
            } | Select-Object -First 1
            
            if ($wifiAdapter) {
                Write-Host "Found WiFi adapter: $($wifiAdapter.Name)" -ForegroundColor Green
                
                # Try to get available networks using WMI
                $availableNetworks = Get-WmiObject -Class MSNdis_80211_BSSIList -Namespace "root\wmi" -ErrorAction SilentlyContinue
                
                if ($availableNetworks) {
                    Write-Host "Found networks via WMI!" -ForegroundColor Green
                    $availableNetworks | ForEach-Object {
                        Write-Host "Network: $($_.Ssid)" -ForegroundColor White
                    }
                } else {
                    Write-Host "WMI method didn't return networks" -ForegroundColor Yellow
                }
            }
        } catch {
            Write-Host "WMI method failed: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        Write-Host ""
        Write-Host "Method 2: Using netsh wlan show profiles (saved networks only)" -ForegroundColor Cyan
        
        # Get saved profiles
        $savedProfiles = netsh wlan show profiles | Select-String "All User Profile" | ForEach-Object { 
            ($_ -split ":")[1].Trim() 
        }
        
        if ($savedProfiles) {
            Write-Host "Saved WiFi Profiles:" -ForegroundColor Green
            $index = 1
            foreach ($profile in $savedProfiles) {
                Write-Host "$index) $profile" -ForegroundColor White
                $index++
            }
        } else {
            Write-Host "No saved profiles found" -ForegroundColor Gray
        }
        
        Write-Host ""
        Write-Host "Method 3: Using PowerShell cmdlets" -ForegroundColor Cyan
        
        try {
            # Try newer PowerShell cmdlets (Windows 10+)
            if (Get-Command Get-NetAdapter -ErrorAction SilentlyContinue) {
                $wifiAdapters = Get-NetAdapter | Where-Object { 
                    $_.InterfaceDescription -like "*Wireless*" -or 
                    $_.InterfaceDescription -like "*WiFi*" -or 
                    $_.InterfaceDescription -like "*802.11*" 
                }
                
                foreach ($adapter in $wifiAdapters) {
                    Write-Host "Adapter: $($adapter.Name) - $($adapter.InterfaceDescription)" -ForegroundColor Green
                    Write-Host "Status: $($adapter.Status)" -ForegroundColor White
                }
            }
        } catch {
            Write-Host "PowerShell cmdlet method failed: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        Write-Host ""
        Write-Host "Method 4: Alternative scanning approaches" -ForegroundColor Cyan
        
        # Show current interface status
        Write-Host "Current WiFi interface status:" -ForegroundColor Yellow
        netsh wlan show interfaces
        
        Write-Host ""
        Write-Host "To see available networks in Windows:" -ForegroundColor Yellow
        Write-Host "1. Open Windows Settings > Network & Internet > WiFi" -ForegroundColor White
        Write-Host "2. Click 'Show available networks' in system tray" -ForegroundColor White
        Write-Host "3. Use: netsh wlan show profiles" -ForegroundColor White
        Write-Host "4. For advanced scanning, use WSL with aircrack-ng" -ForegroundColor White
        
        return $savedProfiles
        
    } catch {
        Write-Host "Error during network scan: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

function Test-WiFiCapabilities {
    Write-Host ""
    Write-Host "Testing WiFi Capabilities..." -ForegroundColor Green
    Write-Host ""
    
    # Test 1: Check if we can access WiFi interfaces
    try {
        $adapters = Get-NetAdapter | Where-Object { 
            $_.InterfaceDescription -like "*Wireless*" -or 
            $_.InterfaceDescription -like "*WiFi*" -or 
            $_.InterfaceDescription -like "*802.11*" 
        }
        
        Write-Host "WiFi Adapters Found: $($adapters.Count)" -ForegroundColor Cyan
        foreach ($adapter in $adapters) {
            Write-Host "  - $($adapter.Name): $($adapter.InterfaceDescription)" -ForegroundColor White
            Write-Host "    Status: $($adapter.Status)" -ForegroundColor Gray
            Write-Host "    MAC: $($adapter.MacAddress)" -ForegroundColor Gray
        }
    } catch {
        Write-Host "Error getting WiFi adapters: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host ""
    
    # Test 2: Check netsh capabilities
    try {
        Write-Host "Testing netsh wlan capabilities..." -ForegroundColor Cyan
        $wlanCapabilities = netsh wlan show drivers
        Write-Host "netsh wlan drivers info retrieved successfully" -ForegroundColor Green
    } catch {
        Write-Host "netsh wlan failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host ""
    
    # Test 3: Check if we're running as admin
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    $isAdmin = $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
    
    Write-Host "Running as Administrator: $isAdmin" -ForegroundColor $(if ($isAdmin) { 'Green' } else { 'Yellow' })
    
    if (!$isAdmin) {
        Write-Host "Note: Some WiFi operations require Administrator privileges" -ForegroundColor Yellow
    }
}

function Show-WiFiCommands {
    Write-Host ""
    Write-Host "Useful WiFi Commands for Windows:" -ForegroundColor Green
    Write-Host "=================================" -ForegroundColor Green
    Write-Host ""
    
    Write-Host "Basic Commands:" -ForegroundColor Yellow
    Write-Host "netsh wlan show interfaces" -ForegroundColor White
    Write-Host "netsh wlan show profiles" -ForegroundColor White
    Write-Host "netsh wlan show drivers" -ForegroundColor White
    Write-Host "netsh wlan disconnect" -ForegroundColor White
    Write-Host "netsh wlan connect name=`"NetworkName`"" -ForegroundColor White
    Write-Host ""
    
    Write-Host "Advanced Commands:" -ForegroundColor Yellow
    Write-Host "netsh wlan show profile name=`"NetworkName`" key=clear" -ForegroundColor White
    Write-Host "netsh wlan export profile folder=C:\WiFiProfiles" -ForegroundColor White
    Write-Host "netsh wlan delete profile name=`"NetworkName`"" -ForegroundColor White
    Write-Host ""
    
    Write-Host "PowerShell Commands:" -ForegroundColor Yellow
    Write-Host "Get-NetAdapter | Where-Object InterfaceDescription -like '*Wireless*'" -ForegroundColor White
    Write-Host "Get-NetConnectionProfile" -ForegroundColor White
    Write-Host ""
}

# Run the tests
Test-WiFiCapabilities
Write-Host ""
Get-AvailableWiFiNetworks
Write-Host ""
Show-WiFiCommands

Write-Host ""
Write-Host "Scan completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Note: Windows has limitations for live WiFi scanning via command line." -ForegroundColor Yellow
Write-Host "For advanced penetration testing, use WSL with Linux tools like aircrack-ng." -ForegroundColor Yellow
