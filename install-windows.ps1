# WiFi Hacker for Windows - Installation Script
# This script helps set up the environment for WiFi Hacker

param(
    [switch]$InstallWSL,
    [switch]$InstallKali,
    [switch]$InstallTools,
    [switch]$All,
    [switch]$Help
)

$script:Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
    White = "White"
}

function Show-Banner {
    Clear-Host
    Write-Host "=================================================================" -ForegroundColor Blue
    Write-Host "    WiFi Hacker for Windows - Installation Script" -ForegroundColor Blue
    Write-Host "    Setting up your penetration testing environment" -ForegroundColor Blue
    Write-Host "=================================================================" -ForegroundColor Blue
    Write-Host ""
}

function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Test-WSLInstalled {
    try {
        $wslVersion = wsl --version 2>$null
        return $true
    }
    catch {
        return $false
    }
}

function Install-WSLFeature {
    Write-Host "Installing WSL (Windows Subsystem for Linux)..." -ForegroundColor Yellow
    
    if (!(Test-Administrator)) {
        Write-Host "ERROR: Administrator privileges required to install WSL!" -ForegroundColor Red
        return $false
    }
    
    try {
        # Enable WSL feature
        dism.exe /online /enable-feature /featurename:Microsoft-Windows-Subsystem-Linux /all /norestart
        
        # Enable Virtual Machine Platform
        dism.exe /online /enable-feature /featurename:VirtualMachinePlatform /all /norestart
        
        # Install WSL 2
        wsl --install --no-distribution
        
        Write-Host "WSL installation completed!" -ForegroundColor Green
        Write-Host "Please restart your computer before continuing." -ForegroundColor Yellow
        return $true
    }
    catch {
        Write-Host "Error installing WSL: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Install-KaliLinux {
    Write-Host "Installing Kali Linux distribution..." -ForegroundColor Yellow
    
    if (!(Test-WSLInstalled)) {
        Write-Host "ERROR: WSL must be installed first!" -ForegroundColor Red
        return $false
    }
    
    try {
        # Install Kali Linux
        wsl --install -d kali-linux
        
        Write-Host "Kali Linux installation completed!" -ForegroundColor Green
        Write-Host "Please set up your username and password when prompted." -ForegroundColor Yellow
        return $true
    }
    catch {
        Write-Host "Error installing Kali Linux: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Install-PenetrationTestingTools {
    Write-Host "Installing penetration testing tools in Kali Linux..." -ForegroundColor Yellow
    
    try {
        # Update package list
        Write-Host "Updating package list..." -ForegroundColor Cyan
        wsl -d kali-linux sudo apt update
        
        # Upgrade system
        Write-Host "Upgrading system packages..." -ForegroundColor Cyan
        wsl -d kali-linux sudo apt upgrade -y
        
        # Install aircrack-ng suite
        Write-Host "Installing aircrack-ng suite..." -ForegroundColor Cyan
        wsl -d kali-linux sudo apt install -y aircrack-ng
        
        # Install reaver
        Write-Host "Installing reaver..." -ForegroundColor Cyan
        wsl -d kali-linux sudo apt install -y reaver
        
        # Install wifite
        Write-Host "Installing wifite..." -ForegroundColor Cyan
        wsl -d kali-linux sudo apt install -y wifite
        
        # Install hashcat
        Write-Host "Installing hashcat..." -ForegroundColor Cyan
        wsl -d kali-linux sudo apt install -y hashcat
        
        # Install additional tools
        Write-Host "Installing additional tools..." -ForegroundColor Cyan
        wsl -d kali-linux sudo apt install -y macchanger wireless-tools net-tools
        
        Write-Host "All tools installed successfully!" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Error installing tools: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Test-Installation {
    Write-Host "Testing installation..." -ForegroundColor Yellow
    Write-Host ""
    
    # Test PowerShell
    Write-Host "PowerShell Version: $($PSVersionTable.PSVersion)" -ForegroundColor Green
    
    # Test Administrator rights
    $isAdmin = Test-Administrator
    Write-Host "Administrator Rights: $(if ($isAdmin) { 'Yes' } else { 'No' })" -ForegroundColor $(if ($isAdmin) { 'Green' } else { 'Red' })
    
    # Test WSL
    $wslInstalled = Test-WSLInstalled
    Write-Host "WSL Installed: $(if ($wslInstalled) { 'Yes' } else { 'No' })" -ForegroundColor $(if ($wslInstalled) { 'Green' } else { 'Red' })
    
    if ($wslInstalled) {
        # Test Kali Linux
        try {
            $kaliTest = wsl -d kali-linux echo "Kali Linux is working" 2>$null
            $kaliWorking = $kaliTest -eq "Kali Linux is working"
            Write-Host "Kali Linux: $(if ($kaliWorking) { 'Working' } else { 'Not Available' })" -ForegroundColor $(if ($kaliWorking) { 'Green' } else { 'Red' })
            
            if ($kaliWorking) {
                # Test aircrack-ng
                try {
                    $aircrackTest = wsl -d kali-linux which aircrack-ng 2>$null
                    $aircrackInstalled = $aircrackTest -ne ""
                    Write-Host "Aircrack-ng: $(if ($aircrackInstalled) { 'Installed' } else { 'Not Installed' })" -ForegroundColor $(if ($aircrackInstalled) { 'Green' } else { 'Red' })
                }
                catch {
                    Write-Host "Aircrack-ng: Not Installed" -ForegroundColor Red
                }
                
                # Test reaver
                try {
                    $reaverTest = wsl -d kali-linux which reaver 2>$null
                    $reaverInstalled = $reaverTest -ne ""
                    Write-Host "Reaver: $(if ($reaverInstalled) { 'Installed' } else { 'Not Installed' })" -ForegroundColor $(if ($reaverInstalled) { 'Green' } else { 'Red' })
                }
                catch {
                    Write-Host "Reaver: Not Installed" -ForegroundColor Red
                }
            }
        }
        catch {
            Write-Host "Kali Linux: Not Available" -ForegroundColor Red
        }
    }
    
    Write-Host ""
    Write-Host "Installation test completed!" -ForegroundColor Green
}

function Show-Help {
    Show-Banner
    Write-Host "WiFi Hacker for Windows - Installation Help" -ForegroundColor Green
    Write-Host "===========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "USAGE:" -ForegroundColor Yellow
    Write-Host "  .\install-windows.ps1 [OPTIONS]" -ForegroundColor White
    Write-Host ""
    Write-Host "OPTIONS:" -ForegroundColor Yellow
    Write-Host "  -InstallWSL     Install Windows Subsystem for Linux" -ForegroundColor White
    Write-Host "  -InstallKali    Install Kali Linux distribution" -ForegroundColor White
    Write-Host "  -InstallTools   Install penetration testing tools" -ForegroundColor White
    Write-Host "  -All            Install everything (WSL + Kali + Tools)" -ForegroundColor White
    Write-Host "  -Help           Show this help message" -ForegroundColor White
    Write-Host ""
    Write-Host "EXAMPLES:" -ForegroundColor Yellow
    Write-Host "  .\install-windows.ps1 -All" -ForegroundColor White
    Write-Host "  .\install-windows.ps1 -InstallWSL" -ForegroundColor White
    Write-Host "  .\install-windows.ps1 -InstallTools" -ForegroundColor White
    Write-Host ""
    Write-Host "NOTES:" -ForegroundColor Yellow
    Write-Host "- Administrator privileges required for WSL installation" -ForegroundColor White
    Write-Host "- Computer restart may be required after WSL installation" -ForegroundColor White
    Write-Host "- Internet connection required for downloading components" -ForegroundColor White
}

function Show-Menu {
    do {
        Show-Banner
        Write-Host "Installation Menu:" -ForegroundColor Green
        Write-Host "==================" -ForegroundColor Green
        Write-Host "1) Install WSL (Windows Subsystem for Linux)" -ForegroundColor White
        Write-Host "2) Install Kali Linux Distribution" -ForegroundColor White
        Write-Host "3) Install Penetration Testing Tools" -ForegroundColor White
        Write-Host "4) Install Everything (Recommended)" -ForegroundColor White
        Write-Host "5) Test Current Installation" -ForegroundColor White
        Write-Host "6) Exit" -ForegroundColor White
        Write-Host ""
        
        $choice = Read-Host "Select option (1-6)"
        
        switch ($choice) {
            "1" { 
                Install-WSLFeature
                Read-Host "Press Enter to continue"
            }
            "2" { 
                Install-KaliLinux
                Read-Host "Press Enter to continue"
            }
            "3" { 
                Install-PenetrationTestingTools
                Read-Host "Press Enter to continue"
            }
            "4" {
                Write-Host "Installing complete environment..." -ForegroundColor Yellow
                if (Install-WSLFeature) {
                    Write-Host "Please restart your computer and run this script again to continue with Kali Linux installation." -ForegroundColor Yellow
                }
                Read-Host "Press Enter to continue"
            }
            "5" { 
                Test-Installation
                Read-Host "Press Enter to continue"
            }
            "6" { 
                Write-Host "Installation script completed!" -ForegroundColor Green
                exit
            }
            default { 
                Write-Host "Invalid option!" -ForegroundColor Red
                Start-Sleep 2
            }
        }
    } while ($true)
}

# Main execution
Show-Banner

if ($Help) {
    Show-Help
    exit
}

if ($All) {
    Write-Host "Installing complete WiFi Hacker environment..." -ForegroundColor Yellow
    
    if (Install-WSLFeature) {
        Write-Host "WSL installed. Please restart your computer." -ForegroundColor Yellow
        Write-Host "After restart, run this script again with -InstallKali and -InstallTools" -ForegroundColor Yellow
    }
    exit
}

if ($InstallWSL) {
    Install-WSLFeature
    exit
}

if ($InstallKali) {
    Install-KaliLinux
    exit
}

if ($InstallTools) {
    Install-PenetrationTestingTools
    exit
}

# If no parameters provided, show interactive menu
Show-Menu
