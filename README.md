Shell Script For Attacking Wireless Connections Using Built-In Kali Tools. Supports All Securities (WEP, WPS, WPA, WPA2)

## 🆕 Windows Version Available!

A Windows PowerShell adaptation is now available! See the Windows-specific files:
- `wifi-hacker-windows.ps1` - Main PowerShell script
- `wifi-hacker-windows.bat` - Easy launcher
- `README-Windows.md` - Windows-specific documentation
- `install-windows.ps1` - Automated setup script
- `test-windows.ps1` - System compatibility test

The Windows version provides both basic WiFi tools using native Windows commands and advanced penetration testing features through WSL (Windows Subsystem for Linux).


![Image](http://i.imgur.com/AjQIOik.jpg)
<br/><br/>
![Image](http://i.imgur.com/VK4Jd4v.jpg)
<br/><br/>
![Image](http://i.imgur.com/92EReev.jpg)
<br/><br/>
![Image](http://i.imgur.com/U7GG5qz.jpg)
<br/><br/>
![WEP](http://i.imgur.com/LF1g15f.jpg)
<br/><br/>
![Image](http://i.imgur.com/80ImpOo.jpg)
<br/><br/>
![Image](http://i.imgur.com/aBQVYqe.jpg)
<br/><br/>
![Image](http://i.imgur.com/8IA7NSg.jpg)
<br/><br/>
![Image](http://i.imgur.com/ItblUIv.jpg)
<br/><br/>
![Image](http://i.imgur.com/msIXnMB.jpg)
<br/><br/>
![Image](http://i.imgur.com/KEwNXH6.jpg)
<br/><br/>
![Image](http://i.imgur.com/WOKuzWc.jpg)
<br/><br/>
![Image](http://i.imgur.com/O8V5zLn.jpg)
<br/><br/>
![Image](http://i.imgur.com/I1XYuIu.jpg)
<br/><br/>
![EXTRAS](http://i.imgur.com/mqJpIAI.jpg)
<br/><br/>
